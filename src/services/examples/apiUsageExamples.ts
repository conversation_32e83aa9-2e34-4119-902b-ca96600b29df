/**
 * API Usage Examples - Best Practices with Dependency Injection
 * 
 * This file demonstrates the recommended patterns for using the API service
 * with proper dependency injection in different scenarios.
 */

import { ApiFactory } from '../apiFactory';
import { ApiClient } from '../api';
import type { IStorageRepository, IApiConfigService } from '../types';

// ============================================================================
// 1. BASIC USAGE - Most common scenario
// ============================================================================

/**
 * Basic usage with default singleton
 * Use this for most application needs
 */
export function basicUsageExample() {
  // Get the default configured API client
  const api = ApiFactory.getApiClient();
  
  // Use it for API calls
  return api.get('/users');
}

// ============================================================================
// 2. CUSTOM CONFIGURATION - Runtime configuration
// ============================================================================

/**
 * Create API client with custom configuration
 * Useful when you need different timeout/retry settings
 */
export function customConfigExample() {
  const api = ApiFactory.createApiClient(
    undefined, // Use default config
    undefined, // Use default storage
    undefined, // Use default config service
    {
      timeout: 30000,      // 30 second timeout
      retryAttempts: 5,    // 5 retry attempts
      baseUrl: 'https://custom-api.example.com' // Override base URL
    }
  );
  
  return api.post('/data', { key: 'value' });
}

// ============================================================================
// 4. TESTING - Mock dependencies for unit tests
// ============================================================================

/**
 * Mock storage repository for testing
 */
class MockStorageRepository implements IStorageRepository {
  private storage = new Map<string, string>();

  async getItem(key: string): Promise<string | null> {
    return this.storage.get(key) || null;
  }

  async setItem(key: string, value: string): Promise<void> {
    this.storage.set(key, value);
  }

  async removeItem(key: string): Promise<void> {
    this.storage.delete(key);
  }
}

/**
 * Mock API config service for testing
 */
class MockApiConfigService implements IApiConfigService {
  async loadApiConfig() {
    return {
      apiKey: 'test-api-key',
      apiBaseUrl: 'https://test-api.example.com'
    };
  }

  async saveApiKey(apiKey: string): Promise<void> {
    // Mock implementation
  }

  async saveApiBaseUrl(url: string): Promise<void> {
    // Mock implementation
  }
}

// ============================================================================
// 5. SERVICE LAYER PATTERN - Recommended for business logic
// ============================================================================

/**
 * User service example showing proper service layer pattern
 */
export class UserService {
  private api: ApiClient;

  constructor(apiClient?: ApiClient) {
    // Use injected client or default singleton
    this.api = apiClient || ApiFactory.getApiClient();
  }

  async getUser(id: string) {
    const response = await this.api.get(`/users/${id}`);
    return response.data;
  }

  async createUser(userData: any) {
    const response = await this.api.post('/users', userData);
    return response.data;
  }

  async updateUser(id: string, userData: any) {
    const response = await this.api.put(`/users/${id}`, userData);
    return response.data;
  }

  async deleteUser(id: string) {
    const response = await this.api.delete(`/users/${id}`);
    return response.data;
  }
}

// ============================================================================
// 6. REACT HOOK PATTERN - For React applications
// ============================================================================

/**
 * Custom hook for API client (if using React)
 * This would typically be in a separate hooks file
 */
export function useApiClient(options?: { 
  timeout?: number; 
  retryAttempts?: number; 
  baseUrl?: string; 
}) {
  // Create or get API client with options
  if (options) {
    return ApiFactory.createApiClient(undefined, undefined, undefined, options);
  }
  
  return ApiFactory.getApiClient();
}

// ============================================================================
// 7. ERROR HANDLING PATTERNS
// ============================================================================

/**
 * Example of proper error handling with the API client
 */
export async function errorHandlingExample() {
  try {
    const api = ApiFactory.getApiClient();
    const response = await api.get('/might-fail');
    return response.data;
  } catch (error) {
    if (error instanceof Error) {
      console.error('API Error:', error.message);
      // Handle specific error types
      if (error.name === 'ApiError') {
        // Handle API-specific errors
        console.error('API Error Details:', error);
      }
    }
    throw error; // Re-throw for caller to handle
  }
}
