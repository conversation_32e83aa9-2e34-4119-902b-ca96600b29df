/**
 * API Factory - Dependency Injection Container
 * 
 * Provides a clean factory pattern for creating API clients with proper dependency injection.
 * Follows SOLID principles and allows for easy testing and configuration management.
 */

import config, { type EnvConfig } from '@/config';
import { ApiClient } from './api';
import { secureStorageRepository } from '@/repositories/secureStorageRepository';
import { apiConfigService } from './apiConfigService';
import type { IStorageRepository, IApiConfigService, ApiClientOptions } from '@/types';

/**
 * API Factory class for dependency injection
 */
export class ApiFactory {
  private static instance: ApiClient | null = null;
  
  /**
   * Create a new API client with injected dependencies
   */
  static createApiClient(
    envConfig: EnvConfig = config,
    storageRepository: IStorageRepository = secureStorageRepository,
    configService: IApiConfigService = apiConfigService,
    options: ApiClientOptions = {}
  ): ApiClient {
    return new ApiClient(envConfig, storageRepository, configService, options);
  }

  /**
   * Get or create singleton API client instance
   * Use this for most application needs
   */
  static getApiClient(
    envConfig: EnvConfig = config,
    storageRepository: IStorageRepository = secureStorageRepository,
    configService: IApiConfigService = apiConfigService,
    options: ApiClientOptions = {}
  ): ApiClient {
    if (!this.instance) {
      this.instance = this.createApiClient(envConfig, storageRepository, configService, options);
    }
    return this.instance;
  }

  /**
   * Reset singleton instance (useful for testing)
   */
  static resetInstance(): void {
    this.instance = null;
  }

  /**
   * Create API client for testing with mock dependencies
   */
  static createTestApiClient(
    mockConfig: Partial<EnvConfig> = {},
    mockStorage: IStorageRepository,
    mockConfigService: IApiConfigService,
    options: ApiClientOptions = {}
  ): ApiClient {
    const testConfig: EnvConfig = {
      ENV_NAME: 'test',
      API_URL: 'https://test-api.example.com',
      X_PUBLIC_KEY: 'test-key',
      DEBUG: true,
      CONFIG_VERSION: '1.0.0',
      FEATURE_X_ENABLED: false,
      ...mockConfig
    };

    return this.createApiClient(testConfig, mockStorage, mockConfigService, options);
  }
}

/**
 * Default singleton instance - use this for most application needs
 */
export const apiClient = ApiFactory.getApiClient();

/**
 * Export factory for custom instantiation
 */
export { ApiFactory as default };
