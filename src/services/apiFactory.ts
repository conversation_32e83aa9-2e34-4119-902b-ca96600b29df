/**
 * API Factory - Dependency Injection Container
 * 
 * Provides a clean factory pattern for creating API clients with proper dependency injection.
 * Follows SOLID principles and allows for easy testing and configuration management.
 */

import config, { type EnvConfig } from '@/config';
import { ApiClient } from './api';
import { secureStorageRepository } from '@/repositories/secureStorageRepository';
import { apiConfigService } from './apiConfigService';
import type { IStorageRepository, IApiConfigService, ApiClientOptions } from './types';

/**
 * API Factory class for dependency injection
 */
export class ApiFactory {
  private static instance: ApiClient | null = null;
  
  /**
   * Create a new API client with injected dependencies
   */
  static createApiClient(
    envConfig: EnvConfig = config,
    storageRepository: IStorageRepository = secureStorageRepository,
    configService: IApiConfigService = apiConfigService,
    options: ApiClientOptions = {}
  ): ApiClient {
    return new ApiClient(envConfig, storageRepository, configService, options);
  }

  /**
   * Get or create singleton API client instance
   * Use this for most application needs
   */
  static getApiClient(
    envConfig: EnvConfig = config,
    storageRepository: IStorageRepository = secureStorageRepository,
    configService: IApiConfigService = apiConfigService,
    options: ApiClientOptions = {}
  ): ApiClient {
    if (!this.instance) {
      this.instance = this.createApiClient(envConfig, storageRepository, configService, options);
    }
    return this.instance;
  }

  /**
   * Reset singleton instance (useful for testing)
   */
  static resetInstance(): void {
    this.instance = null;
  }
}

/**
 * Default singleton instance - use this for most application needs
 */
export const apiClient = ApiFactory.getApiClient();

/**
 * Export factory for custom instantiation
 */
export { ApiFactory as default };
