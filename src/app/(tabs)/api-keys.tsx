import React, { useState, useEffect } from 'react';
import { View, TextInput, Alert, ScrollView, StyleSheet } from 'react-native';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import CustomButton from '@/components/ui/CustomButton';
import { useThemeColor } from '@/hooks/useThemeColor';
import { apiConfigService, type TestResults, type ApiConfig } from '@/services/apiConfigService';

// __DEV__ is a global variable in React Native
declare const __DEV__: boolean;

/// DEV-ONLY; AI-generated, lightly reviewed; screen to test/show API key functionality; basic API call
export default function ApiKeysScreen() {
	const [apiKey, setApiKey] = useState('');
	const [apiBaseUrl, setApiBaseUrl] = useState('');
	const [storedConfig, setStoredConfig] = useState<ApiConfig>({ apiKey: null, apiBaseUrl: null });
	const [isLoading, setIsLoading] = useState(false);
	const [testResults, setTestResults] = useState<TestResults>({
		save: null,
		retrieve: null,
		delete: null,
		exists: null,
		saveUrl: null,
		retrieveUrl: null,
		apiTest: null,
	});
	const [apiTestResponse, setApiTestResponse] = useState<string>('');

	const textColor = useThemeColor({}, 'text');
	const borderColor = useThemeColor({ light: '#ccc', dark: '#444' }, 'text'); // Using 'text' as fallback since 'border' isn't defined

	// Load stored API configuration on component mount
	useEffect(() => {
		loadStoredApiConfig();
		// Note: Dev config auto-loading is disabled to prevent Metro bundler errors
		// Users can manually click "Load Dev Config" button if they have a dev.config.json file
	}, []);

	const loadStoredApiConfig = async () => {
		try {
			const config = await apiConfigService.loadApiConfig();
			setStoredConfig(config);
		} catch (error) {
			console.error('Failed to load stored API configuration:', error);
		}
	};

	const loadDevConfig = async (forceReload = false) => {
		try {
			setIsLoading(true);
			console.log('🔧 [iOS Debug] Starting dev config load, forceReload:', forceReload);

			const result = await apiConfigService.loadDevConfig(forceReload);
			console.log('🔧 [iOS Debug] Dev config result:', result);

			if (result.success) {
				// Reload the stored config to reflect changes
				await loadStoredApiConfig();

				// Update form fields if config was loaded
				if (result.config && result.config.DEV_UNA_API_KEY && result.config.DEV_UNA_API_BASE_URL) {
					setApiKey(result.config.DEV_UNA_API_KEY);
					setApiBaseUrl(result.config.DEV_UNA_API_BASE_URL);
					console.log('✅ [iOS Debug] Form fields updated with dev config');
				}

				Alert.alert('✅ Dev Config Loaded', result.message, [{ text: 'OK' }]);
			} else {
				// Show alert for debugging on iOS
				console.log('ℹ️ [iOS Debug] Dev config not available:', result.message);
				Alert.alert('ℹ️ Dev Config Info', result.message, [
					{ text: 'OK' },
					{ text: 'Show Details', onPress: () => {
						console.log('🔧 [iOS Debug] Full result object:', JSON.stringify(result, null, 2));
					}}
				]);
			}
		} catch (error) {
			console.error('❌ [iOS Debug] Failed to load dev config:', error);
			Alert.alert('❌ Dev Config Error', `Failed to load dev config: ${error}`, [{ text: 'OK' }]);
		} finally {
			setIsLoading(false);
		}
	};

	const handleSaveApiKey = async () => {
		setIsLoading(true);
		try {
			await apiConfigService.saveApiKey(apiKey);
			await loadStoredApiConfig(); // Reload to update UI
			setTestResults((prev) => ({ ...prev, save: true }));
			Alert.alert('Success', 'API key saved securely!');
		} catch (error: any) {
			setTestResults((prev) => ({ ...prev, save: false }));
			Alert.alert('Error', error.message);
		} finally {
			setIsLoading(false);
		}
	};

	const handleSaveApiBaseUrl = async () => {
		setIsLoading(true);
		try {
			await apiConfigService.saveApiBaseUrl(apiBaseUrl);
			await loadStoredApiConfig(); // Reload to update UI
			setTestResults((prev) => ({ ...prev, saveUrl: true }));
			Alert.alert('Success', 'API base URL saved securely!');
		} catch (error: any) {
			setTestResults((prev) => ({ ...prev, saveUrl: false }));
			Alert.alert('Error', error.message);
		} finally {
			setIsLoading(false);
		}
	};

	const handleTestRetrieve = async () => {
		setIsLoading(true);
		try {
			const result = await apiConfigService.testRetrieveApiKey();
			const isSuccess = result.success && result.retrievedKey === storedConfig.apiKey;
			setTestResults((prev) => ({ ...prev, retrieve: isSuccess }));

			if (isSuccess) {
				Alert.alert('Test Passed', `Retrieved API key: ${result.retrievedKey?.substring(0, 10)}...`);
			} else {
				Alert.alert('Test Failed', result.error || 'Retrieved API key does not match stored value');
			}
		} catch (error: any) {
			setTestResults((prev) => ({ ...prev, retrieve: false }));
			Alert.alert('Test Failed', `Failed to retrieve API key: ${error.message}`);
		} finally {
			setIsLoading(false);
		}
	};

	const handleTestRetrieveUrl = async () => {
		setIsLoading(true);
		try {
			const result = await apiConfigService.testRetrieveApiBaseUrl();
			const isSuccess = result.success && result.retrievedUrl === storedConfig.apiBaseUrl;
			setTestResults((prev) => ({ ...prev, retrieveUrl: isSuccess }));

			if (isSuccess) {
				Alert.alert('Test Passed', `Retrieved API base URL: ${result.retrievedUrl}`);
			} else {
				Alert.alert('Test Failed', result.error || 'Retrieved API base URL does not match stored value');
			}
		} catch (error: any) {
			setTestResults((prev) => ({ ...prev, retrieveUrl: false }));
			Alert.alert('Test Failed', `Failed to retrieve API base URL: ${error.message}`);
		} finally {
			setIsLoading(false);
		}
	};

	const handleTestApiCall = async () => {
		if (!storedConfig.apiKey || !storedConfig.apiBaseUrl) {
			Alert.alert('Error', 'Please save both API key and base URL first');
			return;
		}

		setIsLoading(true);
		setApiTestResponse('Testing...');

		try {
			const result = await apiConfigService.testApiCall(storedConfig.apiKey, storedConfig.apiBaseUrl);
			setTestResults((prev) => ({ ...prev, apiTest: result.success }));

			if (result.success) {
				const responseText = `Status: ${result.status}\n\nResponse:\n${JSON.stringify(result.data, null, 2)}`;
				setApiTestResponse(responseText);
				Alert.alert('API Test Passed', 'API call successful! Check the response below.');
			} else {
				const errorText = result.status
					? `Status: ${result.status}\n\nResponse:\n${JSON.stringify(result.data, null, 2)}`
					: `Error: ${result.error}`;
				setApiTestResponse(errorText);
				Alert.alert('API Test Failed', result.error || 'Unknown error occurred');
			}

		} catch (error: any) {
			setTestResults((prev) => ({ ...prev, apiTest: false }));
			setApiTestResponse(`Error: ${error.message}`);
			Alert.alert('API Test Failed', error.message);
		} finally {
			setIsLoading(false);
		}
	};

	const handleTestExists = async () => {
		setIsLoading(true);
		try {
			const result = await apiConfigService.testApiKeyExists();
			const expectedExists = storedConfig.apiKey !== null;
			const isSuccess = result.success && result.exists === expectedExists;

			setTestResults((prev) => ({ ...prev, exists: isSuccess }));

			if (isSuccess) {
				Alert.alert('Test Passed', `Key existence check passed: ${result.exists ? 'exists' : 'does not exist'}`);
			} else {
				Alert.alert('Test Failed', result.error || `Expected: ${expectedExists}, Got: ${result.exists}`);
			}
		} catch (error: any) {
			setTestResults((prev) => ({ ...prev, exists: false }));
			Alert.alert('Test Failed', `Failed to check key existence: ${error.message}`);
		} finally {
			setIsLoading(false);
		}
	};

	const handleDeleteApiKey = async () => {
		setIsLoading(true);
		try {
			await apiConfigService.deleteApiConfig();
			setStoredConfig({ apiKey: null, apiBaseUrl: null });
			setApiKey('');
			setApiBaseUrl('');
			setTestResults((prev) => ({ ...prev, delete: true }));
			Alert.alert('Success', 'API configuration deleted successfully!');
		} catch (error: any) {
			setTestResults((prev) => ({ ...prev, delete: false }));
			Alert.alert('Error', error.message);
		} finally {
			setIsLoading(false);
		}
	};

	const handleRunAllTests = async () => {
		setIsLoading(true);

		// Reset test results
		setTestResults({
			save: null,
			retrieve: null,
			delete: null,
			exists: null,
			saveUrl: null,
			retrieveUrl: null,
			apiTest: null,
		});
		setApiTestResponse('');

		try {
			const result = await apiConfigService.runAllTests();
			setTestResults(result.results);

			if (result.apiResponse) {
				setApiTestResponse(result.apiResponse);
			}

			const summary = `Retrieve Key: ${result.results.retrieve ? '✅' : '❌'}\nRetrieve URL: ${storedConfig.apiBaseUrl ? (result.results.retrieveUrl ? '✅' : '❌') : 'N/A'}\nExists: ${result.results.exists ? '✅' : '❌'}\nAPI Test: ${storedConfig.apiBaseUrl ? (result.results.apiTest ? '✅' : '❌') : 'N/A'}`;

			Alert.alert(
				result.allPassed ? 'All Tests Passed!' : 'Some Tests Failed',
				summary
			);
		} catch (error: any) {
			Alert.alert('Error', `Test suite failed: ${error.message}`);
		} finally {
			setIsLoading(false);
		}
	};

	const getTestStatusIcon = (result: boolean | null) => {
		if (result === null) return '⏳';
		return result ? '✅' : '❌';
	};

	return (
		<ThemedView style={styles.container}>
			<ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
				<ThemedText style={styles.title}>API Configuration</ThemedText>
				<ThemedText style={styles.subtitle}>
					Manage API keys and base URLs with secure storage
				</ThemedText>

				{/* API Key Input Section */}
				<View style={styles.section}>
					<ThemedText style={styles.sectionTitle}>Enter API Key</ThemedText>
					<TextInput
						style={[styles.input, { borderColor, color: textColor }]}
						value={apiKey}
						onChangeText={setApiKey}
						placeholder='Enter your API key here...'
						placeholderTextColor={borderColor}
						secureTextEntry={true}
						multiline={false}
					/>
					<CustomButton
						variant='primary'
						onPress={handleSaveApiKey}
						disabled={isLoading}
						style={styles.button}
					>
						{isLoading ? 'Saving...' : 'Save API Key Securely'}
					</CustomButton>
				</View>

				{/* API Base URL Input Section */}
				<View style={styles.section}>
					<ThemedText style={styles.sectionTitle}>Enter API Base URL</ThemedText>
					<TextInput
						style={[styles.input, { borderColor, color: textColor }]}
						value={apiBaseUrl}
						onChangeText={setApiBaseUrl}
						placeholder='https://api.example.com'
						placeholderTextColor={borderColor}
						secureTextEntry={false}
						multiline={false}
						keyboardType='url'
						autoCapitalize='none'
						autoCorrect={false}
					/>
					<CustomButton
						variant='primary'
						onPress={handleSaveApiBaseUrl}
						disabled={isLoading}
						style={styles.button}
					>
						{isLoading ? 'Saving...' : 'Save API Base URL Securely'}
					</CustomButton>
				</View>

				{/* Dev Config Section - Only show in development */}
				{__DEV__ && (
					<View style={styles.section}>
						<ThemedText style={styles.sectionTitle}>🔧 Development Tools</ThemedText>
						<ThemedText style={styles.subtitle}>
							Auto-load configuration from dev.config.json
						</ThemedText>
						<CustomButton
							variant='secondary'
							onPress={() => loadDevConfig(true)}
							disabled={isLoading}
							style={styles.button}
						>
							{isLoading ? 'Loading...' : 'Load Dev Config'}
						</CustomButton>

						<CustomButton
							variant='outline'
							onPress={() => {
								console.log('🔧 [iOS Debug] Manual debug info:');
								console.log('- __DEV__:', __DEV__);
								console.log('- Current stored config:', storedConfig);
								console.log('- Form API Key:', apiKey ? '[PRESENT]' : '[EMPTY]');
								console.log('- Form API URL:', apiBaseUrl || '[EMPTY]');
								Alert.alert('Debug Info', 'Check console for debug information', [{ text: 'OK' }]);
							}}
							disabled={isLoading}
							style={[styles.button, { marginTop: 8 }]}
						>
							🐛 Debug Info
						</CustomButton>
					</View>
				)}

				{/* Current Status Section */}
				<View style={styles.section}>
					<ThemedText style={styles.sectionTitle}>Current Status</ThemedText>
					<View style={styles.statusContainer}>
						<ThemedText style={styles.statusText}>
							Stored API Key:{' '}
							{storedConfig.apiKey ? `${storedConfig.apiKey.substring(0, 10)}...` : 'None'}
						</ThemedText>
						<ThemedText style={styles.statusText}>
							Stored API Base URL: {storedConfig.apiBaseUrl ? storedConfig.apiBaseUrl : 'None'}
						</ThemedText>
						<ThemedText style={styles.statusText}>
							Storage Available: ✅ Yes
						</ThemedText>
					</View>
				</View>

				{/* Test Operations Section */}
				<View style={styles.section}>
					<ThemedText style={styles.sectionTitle}>Test Operations</ThemedText>

					<View style={styles.testGrid}>
						<CustomButton
							variant='secondary'
							onPress={handleTestRetrieve}
							disabled={isLoading || !storedConfig.apiKey}
							style={styles.testButton}
						>
							Test Retrieve Key {getTestStatusIcon(testResults.retrieve)}
						</CustomButton>

						<CustomButton
							variant='secondary'
							onPress={handleTestRetrieveUrl}
							disabled={isLoading || !storedConfig.apiBaseUrl}
							style={styles.testButton}
						>
							Test Retrieve URL {getTestStatusIcon(testResults.retrieveUrl)}
						</CustomButton>

						<CustomButton
							variant='secondary'
							onPress={handleTestExists}
							disabled={isLoading}
							style={styles.testButton}
						>
							Test Exists {getTestStatusIcon(testResults.exists)}
						</CustomButton>

						<CustomButton
							variant='secondary'
							onPress={handleDeleteApiKey}
							disabled={isLoading || (!storedConfig.apiKey && !storedConfig.apiBaseUrl)}
							style={styles.testButton}
						>
							Delete All {getTestStatusIcon(testResults.delete)}
						</CustomButton>

						<CustomButton
							variant='primary'
							onPress={handleTestApiCall}
							disabled={isLoading || !storedConfig.apiKey || !storedConfig.apiBaseUrl}
							style={styles.testButton}
						>
							Test API Call {getTestStatusIcon(testResults.apiTest)}
						</CustomButton>

						<CustomButton
							variant='primary'
							onPress={handleRunAllTests}
							disabled={isLoading || !storedConfig.apiKey}
							style={[styles.testButton, styles.fullWidthButton]}
						>
							Run All Tests
						</CustomButton>
					</View>
				</View>

				{/* Test Results Section */}
				<View style={styles.section}>
					<ThemedText style={styles.sectionTitle}>Test Results</ThemedText>
					<View style={styles.resultsContainer}>
						<View style={styles.resultRow}>
							<ThemedText style={styles.resultLabel}>Save API Key:</ThemedText>
							<ThemedText style={styles.resultValue}>
								{getTestStatusIcon(testResults.save)}{' '}
								{testResults.save === null
									? 'Not tested'
									: testResults.save
										? 'Passed'
										: 'Failed'}
							</ThemedText>
						</View>
						<View style={styles.resultRow}>
							<ThemedText style={styles.resultLabel}>Save API URL:</ThemedText>
							<ThemedText style={styles.resultValue}>
								{getTestStatusIcon(testResults.saveUrl)}{' '}
								{testResults.saveUrl === null
									? 'Not tested'
									: testResults.saveUrl
										? 'Passed'
										: 'Failed'}
							</ThemedText>
						</View>
						<View style={styles.resultRow}>
							<ThemedText style={styles.resultLabel}>Retrieve API Key:</ThemedText>
							<ThemedText style={styles.resultValue}>
								{getTestStatusIcon(testResults.retrieve)}{' '}
								{testResults.retrieve === null
									? 'Not tested'
									: testResults.retrieve
										? 'Passed'
										: 'Failed'}
							</ThemedText>
						</View>
						<View style={styles.resultRow}>
							<ThemedText style={styles.resultLabel}>Retrieve API URL:</ThemedText>
							<ThemedText style={styles.resultValue}>
								{getTestStatusIcon(testResults.retrieveUrl)}{' '}
								{testResults.retrieveUrl === null
									? 'Not tested'
									: testResults.retrieveUrl
										? 'Passed'
										: 'Failed'}
							</ThemedText>
						</View>
						<View style={styles.resultRow}>
							<ThemedText style={styles.resultLabel}>Exists Check:</ThemedText>
							<ThemedText style={styles.resultValue}>
								{getTestStatusIcon(testResults.exists)}{' '}
								{testResults.exists === null
									? 'Not tested'
									: testResults.exists
										? 'Passed'
										: 'Failed'}
							</ThemedText>
						</View>
						<View style={styles.resultRow}>
							<ThemedText style={styles.resultLabel}>API Test:</ThemedText>
							<ThemedText style={styles.resultValue}>
								{getTestStatusIcon(testResults.apiTest)}{' '}
								{testResults.apiTest === null
									? 'Not tested'
									: testResults.apiTest
										? 'Passed'
										: 'Failed'}
							</ThemedText>
						</View>
						<View style={styles.resultRow}>
							<ThemedText style={styles.resultLabel}>Delete Operation:</ThemedText>
							<ThemedText style={styles.resultValue}>
								{getTestStatusIcon(testResults.delete)}{' '}
								{testResults.delete === null
									? 'Not tested'
									: testResults.delete
										? 'Passed'
										: 'Failed'}
							</ThemedText>
						</View>
					</View>
				</View>

				{/* API Response Section */}
				{apiTestResponse ? (
					<View style={styles.section}>
						<ThemedText style={styles.sectionTitle}>API Test Response</ThemedText>
						<View style={styles.responseContainer}>
							<ScrollView
								style={styles.responseScrollView}
								showsVerticalScrollIndicator={true}
								nestedScrollEnabled={true}
							>
								<ThemedText style={styles.responseText}>
									{apiTestResponse}
								</ThemedText>
							</ScrollView>
						</View>
					</View>
				) : null}
			</ScrollView>
		</ThemedView>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
	},
	scrollView: {
		flex: 1,
		padding: 20,
	},
	title: {
		fontSize: 28,
		fontWeight: 'bold',
		marginBottom: 8,
		textAlign: 'center',
	},
	subtitle: {
		fontSize: 16,
		opacity: 0.7,
		marginBottom: 30,
		textAlign: 'center',
	},
	section: {
		marginBottom: 30,
	},
	sectionTitle: {
		fontSize: 20,
		fontWeight: '600',
		marginBottom: 15,
	},
	input: {
		borderWidth: 1,
		borderRadius: 8,
		padding: 12,
		fontSize: 16,
		marginBottom: 15,
		minHeight: 50,
	},
	button: {
		marginVertical: 5,
	},
	statusContainer: {
		padding: 15,
		borderRadius: 8,
		backgroundColor: 'rgba(0, 0, 0, 0.05)',
	},
	statusText: {
		fontSize: 16,
		marginBottom: 5,
	},
	testGrid: {
		flexDirection: 'row',
		flexWrap: 'wrap',
		justifyContent: 'space-between',
	},
	testButton: {
		width: '48%',
		marginBottom: 10,
	},
	fullWidthButton: {
		width: '100%',
	},
	resultsContainer: {
		padding: 15,
		borderRadius: 8,
		backgroundColor: 'rgba(0, 0, 0, 0.05)',
	},
	resultRow: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		marginBottom: 8,
	},
	resultLabel: {
		fontSize: 16,
		flex: 1,
	},
	resultValue: {
		fontSize: 16,
		fontWeight: '500',
	},
	responseContainer: {
		backgroundColor: 'rgba(0, 0, 0, 0.05)',
		borderRadius: 8,
		padding: 15,
		maxHeight: 300,
	},
	responseScrollView: {
		maxHeight: 270,
	},
	responseText: {
		fontSize: 12,
		fontFamily: 'monospace',
		lineHeight: 16,
	},
});
